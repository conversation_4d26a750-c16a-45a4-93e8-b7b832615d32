"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Plus, FileText, RefreshCw, Download, ExternalLink } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

import { copyrightTaskApi } from "@/api/copyright-task-api";
import { CopyrightTask, CopyrightTaskStatus } from "@/api/core/types";

// 状态颜色映射
const statusColorMap: Record<CopyrightTaskStatus, "default" | "secondary" | "destructive" | "outline"> = {
  "生成中": "secondary",
  "已完成": "default",
  "生成失败": "destructive",
};

// 状态图标映射
const statusIconMap: Record<CopyrightTaskStatus, React.ReactNode> = {
  "生成中": <RefreshCw className="h-3 w-3 animate-spin" />,
  "已完成": <FileText className="h-3 w-3" />,
  "生成失败": <ExternalLink className="h-3 w-3" />,
};

export default function CopyrightTasks() {
  const [tasks, setTasks] = useState<CopyrightTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const router = useRouter();

  // 格式化日期时间戳为可读格式
  const formatDate = (timestamp?: number): string => {
    if (!timestamp) return "未知时间";
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
  };

  // 加载任务数据
  const loadTasks = async (pageNum: number = 1, append: boolean = false) => {
    try {
      if (pageNum === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const response = await copyrightTaskApi.queryList({ 
        page: pageNum, 
        page_size: 10 
      });

      if (append) {
        setTasks(prev => [...prev, ...response.data]);
      } else {
        setTasks(response.data);
      }

      setHasMore(response.data.length === 10 && response.total > pageNum * 10);
      setPage(pageNum);
    } catch (error) {
      console.error("加载任务列表失败:", error);
      toast.error("加载任务列表失败，请稍后重试");
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadTasks(1, false);
    setRefreshing(false);
    toast.success("数据已刷新");
  };

  // 加载更多
  const handleLoadMore = async () => {
    if (!loadingMore && hasMore) {
      await loadTasks(page + 1, true);
    }
  };

  // 处理下载
  const handleDownload = (task: CopyrightTask) => {
    if (task.download_url) {
      window.open(task.download_url, "_blank");
    } else {
      toast.error("下载链接不可用");
    }
  };

  // 初始化加载
  useEffect(() => {
    loadTasks();
  }, []);

  // 任务卡片组件
  const TaskCard = ({ task }: { task: CopyrightTask }) => (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg font-semibold text-gray-900 line-clamp-2">
            {task.name || "未命名任务"}
          </CardTitle>
          <div className="flex items-center gap-2 ml-4">
            <Badge variant={statusColorMap[task.status || "生成中"]} className="flex items-center gap-1">
              {statusIconMap[task.status || "生成中"]}
              {task.status}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {task.description && (
            <p className="text-sm text-gray-600 line-clamp-3">
              {task.description}
            </p>
          )}
          
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>创建时间: {formatDate(task.create_at)}</span>
            {task.completed_at && (
              <span>完成时间: {formatDate(task.completed_at)}</span>
            )}
          </div>

          {task.status === "已完成" && task.download_url && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleDownload(task)}
              className="w-full mt-3"
            >
              <Download className="h-4 w-4 mr-2" />
              下载文档
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      {/* 页面头部 */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">软著任务管理</h1>
          <p className="text-gray-600 mt-1">管理您的软件著作权申请任务</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? "animate-spin" : ""}`} />
            刷新
          </Button>
          <Button onClick={() => router.push("/protected/copyright-tasks/create")}>
            <Plus className="h-4 w-4 mr-2" />
            创建任务
          </Button>
        </div>
      </div>

      {/* 任务列表 */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : tasks.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无任务</h3>
          <p className="text-gray-600 mb-6">您还没有创建任何软著任务</p>
          <Button onClick={() => router.push("/protected/copyright-tasks/create")}>
            <Plus className="h-4 w-4 mr-2" />
            创建第一个任务
          </Button>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {tasks.map((task) => (
              <TaskCard key={task._id} task={task} />
            ))}
          </div>

          {/* 加载更多 */}
          {hasMore && (
            <div className="text-center mt-8">
              <Button
                variant="outline"
                onClick={handleLoadMore}
                disabled={loadingMore}
              >
                {loadingMore ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    加载中...
                  </>
                ) : (
                  "加载更多"
                )}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
