"use client";

import { useState, useEffect } from "react";
import { Search, Filter, Eye, Star, Calendar, Tag } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// 案例数据类型
interface CopyrightCase {
  id: number;
  title: string;
  type: string;
  description: string;
  imageUrl: string;
  tags: string[];
  rating: number;
  views: number;
  date: string;
  featured: boolean;
}

// 模拟案例数据
const allCasesData: CopyrightCase[] = [
  {
    id: 1,
    title: "智能教育平台",
    type: "教育软件",
    description: "基于AI的智能教育平台，提供个性化学习路径和智能辅导功能，支持多种学习模式和实时互动",
    imageUrl: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop",
    tags: ["AI", "教育", "个性化", "在线学习"],
    rating: 4.8,
    views: 1250,
    date: "2024-01-15",
    featured: true,
  },
  {
    id: 2,
    title: "企业资源管理系统",
    type: "管理软件",
    description: "全面的企业资源规划系统，整合人力资源、财务、供应链等模块，提升企业运营效率",
    imageUrl: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop",
    tags: ["ERP", "企业管理", "资源规划", "数据分析"],
    rating: 4.6,
    views: 980,
    date: "2024-01-10",
    featured: false,
  },
  {
    id: 3,
    title: "智慧医疗平台",
    type: "医疗软件",
    description: "连接医院、医生和患者的智慧医疗平台，提供远程诊疗和健康管理服务",
    imageUrl: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop",
    tags: ["医疗", "远程诊疗", "健康管理", "智慧医疗"],
    rating: 4.9,
    views: 1580,
    date: "2024-01-08",
    featured: true,
  },
  {
    id: 4,
    title: "智能物流管理系统",
    type: "物流软件",
    description: "基于大数据和AI的智能物流管理系统，优化配送路线，提高物流效率",
    imageUrl: "https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?w=600&h=400&fit=crop",
    tags: ["物流", "大数据", "路线优化", "智能调度"],
    rating: 4.5,
    views: 720,
    date: "2024-01-05",
    featured: false,
  },
  {
    id: 5,
    title: "金融风控系统",
    type: "金融软件",
    description: "基于机器学习的金融风险控制系统，实时监控交易风险，保障资金安全",
    imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop",
    tags: ["金融", "风控", "机器学习", "实时监控"],
    rating: 4.7,
    views: 1100,
    date: "2024-01-03",
    featured: true,
  },
  {
    id: 6,
    title: "智能客服系统",
    type: "服务软件",
    description: "基于自然语言处理的智能客服系统，提供24小时自动客服和人工客服无缝切换",
    imageUrl: "https://images.unsplash.com/photo-1553484771-371a605b060b?w=600&h=400&fit=crop",
    tags: ["客服", "NLP", "自动化", "智能对话"],
    rating: 4.4,
    views: 850,
    date: "2024-01-01",
    featured: false,
  },
];

// 软件类型选项
const typeOptions = [
  { value: "all", label: "全部类型" },
  { value: "教育软件", label: "教育软件" },
  { value: "管理软件", label: "管理软件" },
  { value: "医疗软件", label: "医疗软件" },
  { value: "物流软件", label: "物流软件" },
  { value: "金融软件", label: "金融软件" },
  { value: "服务软件", label: "服务软件" },
];

export default function CopyrightCases() {
  const [cases, setCases] = useState<CopyrightCase[]>(allCasesData);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState("all");
  const [sortBy, setSortBy] = useState("date");

  // 过滤和排序案例
  useEffect(() => {
    let filteredCases = allCasesData;

    // 按类型过滤
    if (selectedType !== "all") {
      filteredCases = filteredCases.filter(case_ => case_.type === selectedType);
    }

    // 按搜索词过滤
    if (searchTerm) {
      filteredCases = filteredCases.filter(case_ =>
        case_.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        case_.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        case_.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // 排序
    filteredCases.sort((a, b) => {
      switch (sortBy) {
        case "rating":
          return b.rating - a.rating;
        case "views":
          return b.views - a.views;
        case "date":
        default:
          return new Date(b.date).getTime() - new Date(a.date).getTime();
      }
    });

    setCases(filteredCases);
  }, [searchTerm, selectedType, sortBy]);

  // 案例卡片组件
  const CaseCard = ({ case_ }: { case_: CopyrightCase }) => (
    <Card className="group hover:shadow-lg transition-all duration-300 overflow-hidden">
      <div className="relative">
        <img
          src={case_.imageUrl}
          alt={case_.title}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        {case_.featured && (
          <Badge className="absolute top-3 left-3 bg-yellow-500 hover:bg-yellow-600">
            <Star className="h-3 w-3 mr-1" />
            精选
          </Badge>
        )}
        <div className="absolute top-3 right-3 bg-black/50 text-white px-2 py-1 rounded text-xs flex items-center gap-1">
          <Eye className="h-3 w-3" />
          {case_.views}
        </div>
      </div>
      
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg font-semibold group-hover:text-blue-600 transition-colors">
            {case_.title}
          </CardTitle>
          <Badge variant="outline" className="ml-2 flex-shrink-0">
            {case_.type}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <p className="text-sm text-gray-600 mb-4 line-clamp-3">
          {case_.description}
        </p>
        
        <div className="flex flex-wrap gap-1 mb-4">
          {case_.tags.map((tag, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              <Tag className="h-2 w-2 mr-1" />
              {tag}
            </Badge>
          ))}
        </div>
        
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            {case_.date}
          </div>
          <div className="flex items-center gap-1">
            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
            {case_.rating}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      {/* 页面头部 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">软著案例展示</h1>
        <p className="text-gray-600">
          浏览我们的成功案例，了解不同类型软件的著作权申请文档示例
        </p>
      </div>

      {/* 搜索和过滤 */}
      <div className="flex flex-col sm:flex-row gap-4 mb-8">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="搜索案例标题、描述或标签..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={selectedType} onValueChange={setSelectedType}>
          <SelectTrigger className="w-full sm:w-48">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {typeOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-full sm:w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="date">最新</SelectItem>
            <SelectItem value="rating">评分</SelectItem>
            <SelectItem value="views">浏览量</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 案例网格 */}
      {cases.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的案例</h3>
          <p className="text-gray-600">请尝试调整搜索条件或过滤器</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {cases.map((case_) => (
            <CaseCard key={case_.id} case_={case_} />
          ))}
        </div>
      )}
    </div>
  );
}
