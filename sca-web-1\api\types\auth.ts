// 认证相关类型定义

export interface LoginRequest {
  action: 'login';
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: UserInfo;
}

export interface UserInfo {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'user' | 'guest';
  avatar?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface LogoutRequest {
  action: 'logout';
}

export interface GetUserInfoRequest {
  action: 'getUserInfo';
}

export interface RefreshTokenRequest {
  action: 'refreshToken';
  refreshToken: string;
}

export interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
}

export interface ChangePasswordRequest {
  action: 'changePassword';
  oldPassword: string;
  newPassword: string;
}