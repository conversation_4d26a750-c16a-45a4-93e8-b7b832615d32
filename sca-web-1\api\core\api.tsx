"use client";

import { LOGIN_PATH } from "@/config";
import axios from "axios";
import { toast } from "sonner";
import { ApiParams, ApiResponse } from "./types";
import {BASE_URL} from "@/env";

const api = async <T = any>(params: ApiParams): Promise<T> => {
  const access_token: string | null = localStorage.getItem("token");

  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    ...(access_token && { Authorization: access_token }),
  };

  const url: string = `${BASE_URL}/api`;

  const response = await axios<ApiResponse<T>>({
    method: "post",
    url,
    headers,
    data: params,
  });

  const data: ApiResponse<T> = response.data;

  switch (data.code) {
    case 401:
      throw new Error(data.message);
    case 403:
      toast.error("请先登录！");
      localStorage.removeItem("token");
      window.location.href = LOGIN_PATH;
      return {} as T;
    case 500:
      throw new Error(data.message);
    default:
      return data.data as T;
  }
};

export default api;
