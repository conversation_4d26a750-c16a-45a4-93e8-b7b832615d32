// API请求参数接口
export interface ApiParams {
  resource: string;           // 资源标识
  method_name: string;        // 方法名称
  [key: string]: any;         // 其他参数
}

// API统一响应格式接口
export interface ApiResponse<T = any> {
  code: number;        // 业务状态码 (401/403/500等)
  message: string;     // 响应消息
  data: T;            // 实际返回数据
}

// 业务状态码枚举
export enum BusinessCode {
  SUCCESS = 200,       // 成功
  UNAUTHORIZED = 401,  // 未授权
  FORBIDDEN = 403,     // 禁止访问
  SERVER_ERROR = 500   // 服务器错误
}

// 软著任务状态类型
export type CopyrightTaskStatus = "生成中" | "已完成" | "生成失败";

// 软著任务接口
export interface CopyrightTask {
  _id: string;
  user_id?: string;
  customer_name?: string;
  name?: string;
  description?: string;
  status?: CopyrightTaskStatus;
  document_key?: string;
  create_at?: number;
  completed_at?: number;
  is_deleted?: boolean;
  download_url?: string; // API动态生成的下载链接
}

// 软著任务列表响应接口
export interface CopyrightTaskListResponse {
  total: number;
  page: number;
  page_size: number;
  data: CopyrightTask[];
}

// 软著任务创建参数接口
export interface CreateCopyrightTaskParams {
  name: string;
  description: string;
}

// 软著任务查询参数接口
export interface QueryCopyrightTaskParams {
  page?: number;
  page_size?: number;
}