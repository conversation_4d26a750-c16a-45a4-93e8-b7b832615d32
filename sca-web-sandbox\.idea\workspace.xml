<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7d23e7dc-0b5f-45c6-be59-74b75618c247" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/../sca-web-1/api/types.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../sca-web-1/api/types.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../sca-web-1/app/page.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../sca-web-1/app/page.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../sca-web-1/components/siderbar/sidebar-config.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../sca-web-1/components/siderbar/sidebar-config.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../sca-web-1/config.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../sca-web-1/config.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="30XL573RqjfQKfiOhdqEl1KuJar" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;js.debugger.nextJs.config.created.client&quot;: &quot;true&quot;,
    &quot;js.debugger.nextJs.config.created.server&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/project/software-copyright-agent/sca-web-sandbox/app&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.Next.js: 服务器端.executor&quot;: &quot;Run&quot;,
    &quot;npm.build.executor&quot;: &quot;Run&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Users\\<USER>\\Desktop\\project\\software-copyright-agent\\sca-web-sandbox\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\project\software-copyright-agent\sca-web-sandbox\app" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\project\software-copyright-agent\sca-web-sandbox\app\public" />
      <recent name="C:\Users\<USER>\Desktop\project\software-copyright-agent\sca-web-sandbox\app" />
    </key>
  </component>
  <component name="RunManager" selected="npm.Next.js: 服务器端">
    <configuration name="Next.js: 服务器端" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="build" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="build" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.build" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-WS-251.26927.40" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="7d23e7dc-0b5f-45c6-be59-74b75618c247" name="更改" comment="" />
      <created>1753768784715</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753768784715</updated>
      <workItem from="1753768785791" duration="3013000" />
      <workItem from="1753799268841" duration="49000" />
      <workItem from="1753799833543" duration="5423000" />
      <workItem from="1753806881595" duration="2735000" />
      <workItem from="1753839357905" duration="12644000" />
      <workItem from="1753887322539" duration="6728000" />
      <workItem from="1753929626164" duration="1543000" />
      <workItem from="1753949613479" duration="1194000" />
      <workItem from="1754013278637" duration="2378000" />
      <workItem from="1754095867839" duration="1222000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
    <option name="exactExcludedFiles">
      <list>
        <option value="$PROJECT_DIR$/app/public/home/<USER>" />
      </list>
    </option>
  </component>
</project>