// 认证相关API服务

import api from '@/api/core/api';
import type { 
  LoginRequest, 
  LoginResponse, 
  UserInfo,
  LogoutRequest,
  GetUserInfoRequest,
  RefreshTokenRequest,
  RefreshTokenResponse,
  ChangePasswordRequest
} from '../types/auth';

export const authService = {
  /**
   * 用户登录
   * @param username 用户名
   * @param password 密码
   * @returns Promise<LoginResponse>
   */
  async login(username: string, password: string): Promise<LoginResponse> {
    return await api<LoginResponse>({
      action: 'login',
      username,
      password
    } as LoginRequest);
  },

  /**
   * 用户登出
   * @returns Promise<void>
   */
  async logout(): Promise<void> {
    return await api({
      action: 'logout'
    } as LogoutRequest);
  },

  /**
   * 获取当前用户信息
   * @returns Promise<UserInfo>
   */
  async getUserInfo(): Promise<UserInfo> {
    return await api<UserInfo>({
      action: 'getUserInfo'
    } as GetUserInfoRequest);
  },

  /**
   * 刷新访问令牌
   * @param refreshToken 刷新令牌
   * @returns Promise<RefreshTokenResponse>
   */
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    return await api<RefreshTokenResponse>({
      action: 'refreshToken',
      refreshToken
    } as RefreshTokenRequest);
  },

  /**
   * 修改密码
   * @param oldPassword 旧密码
   * @param newPassword 新密码
   * @returns Promise<void>
   */
  async changePassword(oldPassword: string, newPassword: string): Promise<void> {
    return await api({
      action: 'changePassword',
      oldPassword,
      newPassword
    } as ChangePasswordRequest);
  }
};