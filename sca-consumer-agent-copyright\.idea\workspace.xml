<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9a96ad2b-bbac-41d1-9cfc-ff9735c1a99b" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/../sca-web-1/api/types.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../sca-web-1/api/types.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../sca-web-1/app/page.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../sca-web-1/app/page.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../sca-web-1/app/protected/dashboard/page.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../sca-web-1/components/siderbar/sidebar-config.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../sca-web-1/components/siderbar/sidebar-config.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../sca-web-1/config.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../sca-web-1/config.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30X6g0u2A6bnIR1jiwWMwgNvp2n" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.path_utils.executor": "Run",
    "Python.plant_uml_utils.executor": "Run",
    "Python.run_langgraph_dev_server.executor": "Run",
    "Python.test.executor": "Run",
    "Python.test_chinese_plantuml.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "dev",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/project/software-copyright-agent/sca-consumer-agent-copyright",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.intellij.pycharm.community.ide.impl.configuration.PythonContentEntriesConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\project\software-copyright-agent\sca-consumer-agent-copyright" />
      <recent name="C:\Users\<USER>\Desktop\project\software-copyright-agent\sca-consumer-agent-copyright\agent\workflow2\ttf" />
      <recent name="C:\Users\<USER>\Desktop\project\software-copyright-agent\sca-consumer-agent-copyright\agent\workflow2\sandbox_deploy" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\project\software-copyright-agent\sca-consumer-agent-copyright\agent" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="run_langgraph_dev_server" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="sca-consumer-agent-copyright" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/run_langgraph_dev_server.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.run_langgraph_dev_server" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-PY-243.26574.90" />
        <option value="bundled-python-sdk-c1fac28bca04-4df51de95216-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.26574.90" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="9a96ad2b-bbac-41d1-9cfc-ff9735c1a99b" name="更改" comment="" />
      <created>1753761678150</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753761678150</updated>
      <workItem from="1753761679216" duration="4245000" />
      <workItem from="1753787320592" duration="14102000" />
      <workItem from="1753839357115" duration="17613000" />
      <workItem from="1753887055672" duration="8834000" />
      <workItem from="1753926850267" duration="44222000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/sca_consumer_agent_copyright$plant_uml_utils.coverage" NAME="plant_uml_utils 覆盖结果" MODIFIED="1754028130916" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/agent/utils" />
    <SUITE FILE_PATH="coverage/sca_consumer_agent_copyright$test.coverage" NAME="test 覆盖结果" MODIFIED="1754013263006" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/sca_consumer_agent_copyright$test_chinese_plantuml.coverage" NAME="test_chinese_plantuml 覆盖结果" MODIFIED="1754019629266" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/sca_consumer_agent_copyright$run_langgraph_dev_server.coverage" NAME="run_langgraph_dev_server 覆盖结果" MODIFIED="1754072214571" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/sca_consumer_agent_copyright$path_utils.coverage" NAME="path_utils 覆盖结果" MODIFIED="1753801203966" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/agent/utils" />
  </component>
</project>