"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { ArrowLeft, FileText, Loader2 } from "lucide-react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import { copyrightTaskApi } from "@/api/copyright-task-api";

// 表单验证模式
const formSchema = z.object({
  name: z
    .string()
    .min(1, "请输入任务名称")
    .min(2, "任务名称至少需要2个字符")
    .max(100, "任务名称不能超过100个字符"),
  description: z
    .string()
    .min(1, "请输入需求描述")
    .min(10, "需求描述至少需要10个字符")
    .max(2000, "需求描述不能超过2000个字符"),
});

type FormData = z.infer<typeof formSchema>;

export default function CreateCopyrightTask() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
    },
  });

  // 处理表单提交
  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    
    try {
      await copyrightTaskApi.create(data.name, data.description);
      
      toast.success("任务创建成功", {
        description: "您的软著任务已提交，系统正在处理中",
      });
      
      // 跳转到任务列表页面
      router.push("/protected/copyright-tasks");
    } catch (error: any) {
      console.error("创建任务失败:", error);
      toast.error("创建任务失败", {
        description: error.message || "请稍后重试",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      {/* 页面头部 */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">创建软著任务</h1>
          <p className="text-gray-600 mt-1">填写软件信息，AI将为您生成完整的软件著作权申请文档</p>
        </div>
      </div>

      {/* 创建表单 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            任务信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* 任务名称 */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>任务名称 *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="请输入软件名称，如：智能教育管理系统"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      请输入您要申请软著的软件名称，这将作为申请文档的标题
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 需求描述 */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>需求描述 *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请详细描述您的软件功能、特点和应用场景，例如：
这是一个基于人工智能的教育管理系统，主要功能包括：
1. 学生信息管理
2. 课程安排与管理
3. 成绩统计与分析
4. 智能推荐学习内容
5. 在线考试系统
适用于中小学、培训机构等教育场景..."
                        className="min-h-[200px] resize-none"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      请详细描述软件的功能模块、技术特点、应用场景等信息，描述越详细，生成的文档质量越高
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 提交按钮 */}
              <div className="flex items-center justify-end gap-4 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={isSubmitting}
                >
                  取消
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      创建中...
                    </>
                  ) : (
                    <>
                      <FileText className="h-4 w-4 mr-2" />
                      创建任务
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* 提示信息 */}
      <Card className="mt-6 bg-blue-50 border-blue-200">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
            <div className="space-y-2">
              <h3 className="font-medium text-blue-900">温馨提示</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 任务创建后，AI将自动分析您的需求并生成软著申请文档</li>
                <li>• 生成过程通常需要3-10分钟，请耐心等待</li>
                <li>• 生成完成后，您可以在任务列表中下载完整的申请文档</li>
                <li>• 如有疑问，请联系客服获取帮助</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
