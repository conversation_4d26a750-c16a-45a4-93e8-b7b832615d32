// 仪表板相关API服务

import api from '@/api/core/api';
import type { 
  DashboardStatsRequest,
  DashboardStatsResponse,
  RecentActivitiesRequest,
  RecentActivitiesResponse,
  ActivityType,
  SystemStatusRequest,
  SystemStatusResponse,
  NotificationsRequest,
  NotificationsResponse,
  MarkNotificationReadRequest,
  MarkAllNotificationsReadRequest
} from '../types/dashboard';

export const dashboardService = {
  /**
   * 获取仪表板统计数据
   * @param dateRange 日期范围（可选）
   * @returns Promise<DashboardStatsResponse>
   */
  async getDashboardStats(dateRange?: {
    startDate: string;
    endDate: string;
  }): Promise<DashboardStatsResponse> {
    return await api<DashboardStatsResponse>({
      action: 'getDashboardStats',
      ...(dateRange && { dateRange })
    } as DashboardStatsRequest);
  },

  /**
   * 获取最近活动记录
   * @param params 查询参数
   * @returns Promise<RecentActivitiesResponse>
   */
  async getRecentActivities(params: {
    limit?: number;
    type?: ActivityType[];
  } = {}): Promise<RecentActivitiesResponse> {
    return await api<RecentActivitiesResponse>({
      action: 'getRecentActivities',
      limit: 20,
      ...params
    } as RecentActivitiesRequest);
  },

  /**
   * 获取系统状态
   * @returns Promise<SystemStatusResponse>
   */
  async getSystemStatus(): Promise<SystemStatusResponse> {
    return await api<SystemStatusResponse>({
      action: 'getSystemStatus'
    } as SystemStatusRequest);
  },

  /**
   * 获取通知列表
   * @param params 查询参数
   * @returns Promise<NotificationsResponse>
   */
  async getNotifications(params: {
    page?: number;
    size?: number;
    unreadOnly?: boolean;
  } = {}): Promise<NotificationsResponse> {
    return await api<NotificationsResponse>({
      action: 'getNotifications',
      page: 1,
      size: 10,
      unreadOnly: false,
      ...params
    } as NotificationsRequest);
  },

  /**
   * 标记通知为已读
   * @param notificationId 通知ID
   * @returns Promise<void>
   */
  async markNotificationRead(notificationId: string): Promise<void> {
    return await api({
      action: 'markNotificationRead',
      notificationId
    } as MarkNotificationReadRequest);
  },

  /**
   * 标记所有通知为已读
   * @returns Promise<void>
   */
  async markAllNotificationsRead(): Promise<void> {
    return await api({
      action: 'markAllNotificationsRead'
    } as MarkAllNotificationsReadRequest);
  },

  /**
   * 获取未读通知数量
   * @returns Promise<number>
   */
  async getUnreadNotificationCount(): Promise<number> {
    const result = await this.getNotifications({ 
      page: 1, 
      size: 1, 
      unreadOnly: true 
    });
    return result.unreadCount;
  },

  /**
   * 刷新仪表板数据（组合多个API调用）
   * @param dateRange 日期范围（可选）
   * @returns Promise<{stats: DashboardStatsResponse, activities: RecentActivitiesResponse, systemStatus: SystemStatusResponse}>
   */
  async refreshDashboard(dateRange?: {
    startDate: string;
    endDate: string;
  }): Promise<{
    stats: DashboardStatsResponse;
    activities: RecentActivitiesResponse;
    systemStatus: SystemStatusResponse;
  }> {
    const [stats, activities, systemStatus] = await Promise.all([
      this.getDashboardStats(dateRange),
      this.getRecentActivities({ limit: 10 }),
      this.getSystemStatus()
    ]);

    return {
      stats,
      activities,
      systemStatus
    };
  }
};